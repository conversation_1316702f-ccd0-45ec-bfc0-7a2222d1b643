import { useEffect, useState } from "react";
import { useFetchState } from "@hooks/useFetchState";
import { useTranslation } from "react-i18next";
import {
  IAdjustmentRequest,
  IAdjustmentDetailRequest,
  IUpdateAdjustmentRequest,
  IUpdateAdjustmentDetailRequest,
  IUpdateAdjustmentPathParams,
  IUpdateAdjustmentQuery,
  IGetAdjustmentQuery,
  IAdjustmentResponseDTO,
} from "@modules/adjustment/interfaces/payloads/IAdjustmentPayload";
import { useSnackBar } from "@common";
import { getErrorToDisplay } from "itsf-ui-common";
import { createAdjustment, getAdjustment, updateAdjustment } from "@modules/adjustment/apis/apis";
import { ICreateCreditNoteRequest } from "@modules/financial-document/interfaces/payloads";
import {
  IElligibleInvoiceResponse,
  IPageResponseHelperCreditNoteViewResponse,
} from "@modules/financial-document/interfaces/responses/ICreditNotesResponses";
import { createCreditNote, getEligibleInvoices, getPaginatedNotes } from "@modules/financial-document/apis/v0";
import { TContactDetails } from "../CustomerDetails/ICustomerDetails";

interface IPropsViewAdjusments {
  accountId: string;
  contactDetails: TContactDetails | undefined;
}

export const useViewAdjustments = ({ accountId, contactDetails }: IPropsViewAdjusments) => {
  const { setSnackBarError } = useSnackBar();

  // Estado para almacenar los adjustments obtenidos automáticamente
  const [adjustmentsList, setAdjustmentsList] = useState<IAdjustmentResponseDTO[]>([]);
  const [isLoadingAdjustments, setIsLoadingAdjustments] = useState<boolean>(false);
  const [canApprove, setCanApprove] = useState<boolean>(true);

  // Estado para almacenar las facturas elegibles
  const [eligibleInvoices, setEligibleInvoices] = useState<IElligibleInvoiceResponse[]>([]);

  // Variable para manejar el estado del caseId
  const [caseId, setCaseId] = useState<number>(0);

  /// ***** HELPERS ***** ///
  const nowLocalForLocalDateTime = () => {
    const d = new Date();
    const local = new Date(d.getTime() - d.getTimezoneOffset() * 60000);
    return local.toISOString().slice(0, -1); // remueve la Z
  };

  // Retry simple con backoff para manejar deadlocks/transient errors
  async function withRetry<T>(fn: () => Promise<T>, attempts = 3) {
    let lastErr: unknown;
    for (let i = 0; i < attempts; i++) {
      try {
        return await fn();
      } catch (e: any) {
        lastErr = e;
        const msg = (e?.message || "").toLowerCase();
        const retriable =
          msg.includes("deadlock") ||
          msg.includes("cannotacquirelock") ||
          msg.includes("sqltransactionrollback");
        if (!retriable || i === attempts - 1) throw e;
        await new Promise((r) => setTimeout(r, 150 * Math.pow(2, i)));
      }
    }
    throw lastErr;
  }

  /// ***** METODOS PRIMARIOS PARA HACER UN CRUD DE ADJUSTMENT ****///
  const callAdjusmentCustomServiceCreateAdjusment = async ({
    invoiceId,
    totalAmount,
    createdBy,
    caseId,
    orderId,
    adjustmentTypeId,
    details,
  }: {
    invoiceId: number;
    totalAmount: number;
    createdBy: string;
    caseId: number;
    orderId: string;
    adjustmentTypeId: number;
    details: IAdjustmentDetailRequest[];
  }): Promise<boolean> => {
    try {
      const createAdjusmentPayload: IAdjustmentRequest = {
        invoiceId,
        accountId,
        totalAmount,
        createdBy,
        caseId,
        orderId,
        adjustmentTypeId,
        detail: details,
      };
      await createAdjustment(createAdjusmentPayload);
      return true;
    } catch (error) {
      setSnackBarError(getErrorToDisplay(error));
      return false;
    }
  };

  const callAdjusmentCustomServiceUpdateAdjusment = async ({
    adjustmentId,
    idRubro,
    totalAmount,
    deductSerialNo,
    orderId,
    adjustmentTypeId,
    deniedBy,
    deniedDate,
    approvedBy,
    approvedDate,
    details,
  }: {
    adjustmentId: number;
    idRubro?: number;
    totalAmount?: number;
    deductSerialNo?: string;
    orderId?: string;
    adjustmentTypeId?: number;
    deniedBy?: string;
    deniedDate?: string;
    approvedBy?: string;
    approvedDate?: string;
    details?: IUpdateAdjustmentDetailRequest[];
  }): Promise<boolean> => {
    try {
      const params: IUpdateAdjustmentPathParams = { adjustmentId };
      const query: IUpdateAdjustmentQuery = { idRubro };

      const updateAdjusmentPayload: IUpdateAdjustmentRequest = {
        totalAmount,
        deductSerialNo,
        orderId,
        adjustmentTypeId,
        deniedBy,
        deniedDate,
        approvedBy,
        approvedDate,
        detail: details,
      };

      await updateAdjustment(params, updateAdjusmentPayload, query);
      return true;
    } catch (error) {
      setSnackBarError(getErrorToDisplay(error));
      return false;
    }
  };

  const callAdjusmentCustomServiceGetAdjusment = async ({
    adjustmentId,
    invoiceId,
    caseId,
    status,
  }: {
    adjustmentId?: number | string;
    invoiceId?: number | string;
    caseId?: number | string;
    status?: string;
  }): Promise<IAdjustmentResponseDTO | null> => {
    try {
      const query: IGetAdjustmentQuery = {
        ...(adjustmentId != null ? { adjustmentId } : {}),
        ...(invoiceId != null ? { invoiceId } : {}),
        ...(caseId != null ? { caseId } : {}),
        ...(status != null ? { status } : {}),
      } as IGetAdjustmentQuery;

      const response = await getAdjustment(query);
      return response ?? null;
    } catch (error) {
      setSnackBarError(getErrorToDisplay(error));
      return null;
    }
  };

  /// ***** METODOS PRIMARIOS PARA HACER UN CRUD DE FINANCIAL DOCUMENTS (CREATE, SELECT) ****///
  const callFinancialDocumentsCreateCreditNote = async ({
    invoiceNumber,
    adjustmentType,
    comment,
    services,
  }: ICreateCreditNoteRequest): Promise<boolean> => {
    try {
      await createCreditNote({
        accountId,
        invoiceNumber,
        adjustmentType,
        comment,
        services,
      });
      return true;
    } catch (error) {
      setSnackBarError(getErrorToDisplay(error));
      return false;
    }
  };

  const callFinancialDocumentsGetPaginatedNotes = async (): Promise<IPageResponseHelperCreditNoteViewResponse | null> => {
    try {
      const response = await getPaginatedNotes({ accountId });
      return response ?? null;
    } catch (error) {
      setSnackBarError(getErrorToDisplay(error));
      return null;
    }
  };

  const callFinancialDocumentsGetEligibleInvoices = async (): Promise<IElligibleInvoiceResponse[] | null> => {
    try {
      const response = await getEligibleInvoices({ accountId });
      return response ?? null;
    } catch (error) {
      setSnackBarError(getErrorToDisplay(error));
      return null;
    }
  };

  // Método que se ejecuta automáticamente al crear la instancia
  const loadAdjustmentsFromEligibleInvoices = async (): Promise<void> => {
    if (!accountId) return;

    setIsLoadingAdjustments(true);
    try {
      const fetchedEligibleInvoices = await callFinancialDocumentsGetEligibleInvoices();

      if (!fetchedEligibleInvoices || fetchedEligibleInvoices.length === 0) {
        setAdjustmentsList([]);
        setEligibleInvoices([]);
        return;
      }

      setEligibleInvoices(fetchedEligibleInvoices);

      const invoiceNumbers = fetchedEligibleInvoices
        .map((invoice) => invoice.invoice_number)
        .filter((invoiceNumber) => invoiceNumber != null) as string[];

      const adjustmentsPromises = invoiceNumbers.map(async (invoiceNumber) => {
        try {
          const res = await callAdjusmentCustomServiceGetAdjusment({ invoiceId: invoiceNumber });
          if (Array.isArray(res)) return res;
          return res ?? null;
        } catch (error) {
          console.error(`Error obteniendo adjustment para invoice ${invoiceNumber}:`, error);
          return null;
        }
      });

      const rawResults = await Promise.all(adjustmentsPromises);
      const flatResults: unknown[] = rawResults.flatMap((item) => (Array.isArray(item) ? item : [item]));
      const validAdjustments: IAdjustmentResponseDTO[] = flatResults.filter((adj): adj is IAdjustmentResponseDTO =>
        isAdjustmentResponseDTO(adj),
      );

      setAdjustmentsList(validAdjustments);
    } catch (error) {
      console.error("Error en loadAdjustmentsFromEligibleInvoices:", error);
      setSnackBarError(getErrorToDisplay(error));
      setAdjustmentsList([]);
    } finally {
      setIsLoadingAdjustments(false);
    }
  };

  // --- Type guards ---
  const isAdjustmentDetail = (x: unknown): x is {
    adjustmentId: number;
    idRubro: number;
    amount: number;
    status: string;
    disputeSerialNo?: string;
    adjustmentSerialNo?: string;
  } => {
    if (!x || typeof x !== "object") return false;
    const o = x as any;
    return (
      typeof o.adjustmentId === "number" &&
      typeof o.idRubro === "number" &&
      typeof o.amount === "number" &&
      typeof o.status === "string"
    );
  };

  const isAdjustmentResponseDTO = (x: unknown): x is IAdjustmentResponseDTO => {
    if (!x || typeof x !== "object") return false;
    const o = x as any;
    return (
      typeof o.id === "number" &&
      typeof o.invoiceId === "number" &&
      typeof o.accountId === "string" &&
      typeof o.totalAmount === "number" &&
      Array.isArray(o.detail) &&
      o.detail.every(isAdjustmentDetail) &&
      typeof o.type === "string"
    );
  };

  // useEffect para ejecutar el método automáticamente al crear la instancia
  useEffect(() => {
    loadAdjustmentsFromEligibleInvoices();
  }, [accountId]);

  // Método para crear credit note usando adjustment
  const createCreditNoteFromAdjustment = async ({
    invoiceId,
    details,
  }: {
    invoiceId: number;
    details: { idRubro: number; amount: number }[];
  }): Promise<boolean> => {
    try {
      const totalAmount = details.reduce((sum, detail) => sum + detail.amount, 0);
      const adjustmentDetails: IAdjustmentDetailRequest[] = details.map((detail) => ({
        idRubro: detail.idRubro,
        amount: detail.amount,
      }));

      const result = await callAdjusmentCustomServiceCreateAdjusment({
        invoiceId,
        totalAmount,
        createdBy: "CRM-PROVISIONAL",
        caseId,
        orderId: "",
        adjustmentTypeId: 13,
        details: adjustmentDetails,
      });

      if (result) {
        await loadAdjustmentsFromEligibleInvoices();
      }
      return result;
    } catch (error) {
      console.error("Error creando credit note:", error);
      setSnackBarError(getErrorToDisplay(error));
      return false;
    }
  };

  /**
   * Actualiza el adjustment y sus detalles según el estatus:
   * 1) Actualiza CABECERA primero (una sola vez).
   * 2) Luego actualiza cada DETALLE en SERIE (sin Promise.all).
   * 3) Con retry ante deadlock/transient errors.
   */
  const updateAdjustmentStatus = async ({
    adjustmentId,
    status,
    adjustment,
  }: {
    adjustmentId: number;
    status: "approved" | "denied" | "dispute" | "completed";
    adjustment: IAdjustmentResponseDTO;
  }): Promise<boolean> => {
    try {
      const currentDate = nowLocalForLocalDateTime();

      if (!adjustment.detail || adjustment.detail.length === 0) {
        throw new Error("No hay detalles en el adjustment para actualizar");
      }


      // 1) DETALLES en serie
      for (const detail of adjustment.detail) {
        if (!detail.idRubro) {
          throw new Error(`Detalle sin idRubro encontrado en adjustment ${adjustmentId}`);
        }

        const detailUpdate: IUpdateAdjustmentDetailRequest = {
          status,
          amount: detail.amount,
          disputeSerialNo: detail.disputeSerialNo,
          adjustmentSerialNo: detail.adjustmentSerialNo,
        };

        await withRetry(() =>
          callAdjusmentCustomServiceUpdateAdjusment({
            adjustmentId,
            adjustmentTypeId: adjustment.adjustmentTypeId,
            totalAmount: adjustment.totalAmount,
            idRubro: detail.idRubro,
            details: [detailUpdate],
          }),
        );
      }

      // 2) CABECERA 
      if (status === "approved"|| status === "denied") {
        await withRetry(() =>
            callAdjusmentCustomServiceUpdateAdjusment({
            adjustmentId,
            adjustmentTypeId: adjustment.adjustmentTypeId,
            totalAmount: adjustment.totalAmount,
            approvedBy: status === "approved" ? "CRM-PROVISIONAL" : undefined,
            approvedDate: status === "approved" ? currentDate : undefined,
            deniedBy: status === "denied" ? "CRM-PROVISIONAL" : undefined,
            deniedDate: status === "denied" ? currentDate : undefined,
            // sin idRubro, sin detail
            }),
        );
      }

      await loadAdjustmentsFromEligibleInvoices();
      return true;
    } catch (error) {
      console.error("Error actualizando adjustment:", error);
      setSnackBarError(getErrorToDisplay(error));
      return false;
    }
  };

  return {
    // Métodos existentes
    callAdjusmentCustomServiceGetAdjusment,
    callAdjusmentCustomServiceUpdateAdjusment,
    callAdjusmentCustomServiceCreateAdjusment,
    callFinancialDocumentsCreateCreditNote,
    callFinancialDocumentsGetPaginatedNotes,
    callFinancialDocumentsGetEligibleInvoices,

    // Estados y métodos expuestos
    adjustmentsList,
    isLoadingAdjustments,
    loadAdjustmentsFromEligibleInvoices,
    eligibleInvoices,
    caseId,
    setCaseId,
    createCreditNoteFromAdjustment,
    updateAdjustmentStatus,
    canApprove,
  };
};
