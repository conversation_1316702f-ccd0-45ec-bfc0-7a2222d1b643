<SectionCard
                            icon={<SvgIcon className={iconClasses.main} component={BillIcon} viewBox="0 0 21 19" />}
                            title={t("paymentAndBilling:invoicesDetails")}
                        >
                            {hasExecuteAdjustmentPermission && (
                                <Grid container direction={"row"} spacing={2} width={"100%"}>
                                    <Grid item xs={12}>
                                        <Divider textAlign="center">{t("customer:numberOfCase")}</Divider>
                                    </Grid>
                                    <Grid item xs={4}>
                                        <TextField
                                            fullWidth
                                            label={t("customer:numberOfCase")}
                                            onChange={({ target }) => handleChangeNumberCase({ target })}
                                        />
                                    </Grid>
                                    <Grid item xs={2}>
                                        <Button
                                            color="primary"
                                            variant="outlined"
                                            // onClick={() => verifyUserCase(numberOfCase)}
                                        >
                                            {t("common:verify")}
                                        </Button>
                                    </Grid>
                                    <Grid item xs={12}>
                                        <Divider textAlign="center">{t("paymentAndBilling:invoicesDetails")}</Divider>
                                    </Grid>
                                </Grid>
                            )}
                            <SimpleTable cellTextColor="black">
                                <TableHead>
                                    <TableRow>
                                        {invoiceDetailsTableHeaders.map((header) => (
                                            <TableCell key={header.key}>{t(header.key as any)}</TableCell>
                                        ))}
                                        {hasExecuteAdjustmentPermission && (
                                            <TableCell key="adjust">{t("paymentAndBilling:adjust")}</TableCell>
                                        )}
                                    </TableRow>
                                </TableHead>
                                <TableBody>
                                    {invoicesDetailsCbs?.map((data, idx) => (
                                        <TableRow key={data.invoiceId}>
                                            <TableCell>{idx + 1}</TableCell>
                                            <TableCell>{data.chargeCode}</TableCell>
                                            <TableCell>
                                                <Pricing price={data.chargeAmount} type={EPricingType.FIXED} />
                                            </TableCell>
                                            <TableCell>
                                                <Pricing price={data.openAmount} type={EPricingType.FIXED} />
                                            </TableCell>
                                            <TableCell>
                                                <Pricing price={data.taxAmount} type={EPricingType.FIXED} />
                                            </TableCell>
                                            <TableCell>
                                                <Pricing price={data.openTaxAmount} type={EPricingType.FIXED} />
                                            </TableCell>
                                            {hasExecuteAdjustmentPermission && (
                                                <TableCell>
                                                    <TextField
                                                        disabled={!hasExecuteAdjustmentPermission}
                                                        error={Boolean(data.error)}
                                                        helperText={data.error}
                                                        size="small"
                                                        sx={{ width: 150 }}
                                                        type="number"
                                                        value={data.adjust}
                                                        variant="outlined"
                                                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                            handleInputChange(
                                                                e,
                                                                data.invoiceId,
                                                                data.chargeAmount - data.taxAmount
                                                            )
                                                        }
                                                    />
                                                </TableCell>
                                            )}
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </SimpleTable>
                            <Box display="flex" pt={2}>
                                <Button
                                    color="primary"
                                    variant="outlined"
                                    onClick={() => setInvoicesDetailsCbs(undefined)}
                                >
                                    {t("paymentAndBilling:exit")}
                                </Button>
                                <Typography ml="auto" textTransform="uppercase">
                                    {t("paymentAndBilling:total")}:&nbsp;&nbsp;&nbsp;
                                    <Pricing
                                        price={invoicesDetailsCbs?.reduce(
                                            (accumulator, currentValue) => accumulator + currentValue.chargeAmount,
                                            0
                                        )}
                                        type={EPricingType.FIXED}
                                    />
                                </Typography>
                                {hasExecuteAdjustmentPermission && (
                                    <Typography ml={25} textTransform="uppercase">
                                        {t("paymentAndBilling:totalAdjust")}:&nbsp;&nbsp;&nbsp;
                                        <Pricing
                                            price={invoicesDetailsCbs?.reduce(
                                                (accumulator, currentValue) => accumulator + currentValue.chargeAmount,
                                                0
                                            )}
                                            type={EPricingType.FIXED}
                                        />
                                    </Typography>
                                )}
                            </Box>
                        </SectionCard>
