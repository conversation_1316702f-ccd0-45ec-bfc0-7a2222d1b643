import React, { useState, useEffect } from "react";
import { TContactDetails } from "../CustomerDetails/ICustomerDetails";
import { useViewAdjustments } from "./useViewAdjusments";
import { IElligibleInvoiceResponse } from "@modules/financial-document/interfaces/responses/ICreditNotesResponses";
import { IAdjustmentResponseDTO } from "@modules/adjustment/interfaces/payloads/IAdjustmentPayload"; // ✅ NUEVO
import { useGetQueryParams } from "@hooks/useGetQueryParams";
import { useLocation } from "react-router-dom";
import { useSnackBar } from "@common/SnackBar";

interface IPropsViewAdjusments {
  accountId: string;
  contactDetails: TContactDetails | undefined;
}

const ViewAdjusments = ({ accountId, contactDetails }: IPropsViewAdjusments) => {
  const { caseid: caseIdFromQuery } = useGetQueryParams("caseid");
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const caseIdFromLocation = searchParams.get("caseid");
  const { setSnackBarError } = useSnackBar();

  const {
    adjustmentsList,
    isLoadingAdjustments,
    loadAdjustmentsFromEligibleInvoices,
    eligibleInvoices,
    setCaseId,
    caseId,
    createCreditNoteFromAdjustment,
    updateAdjustmentStatus,
    canApprove, // ✅ NUEVO
  } = useViewAdjustments({ accountId, contactDetails });

  const [showEligibleInvoices, setShowEligibleInvoices] = useState<boolean>(false);
  const [selectedInvoice, setSelectedInvoice] = useState<IElligibleInvoiceResponse | null>(null);
  const [creditNoteAmounts, setCreditNoteAmounts] = useState<{ [key: string]: number }>({});
  const [selectedAdjustment, setSelectedAdjustment] = useState<IAdjustmentResponseDTO | null>(null); // ✅ NUEVO
  const [isAuthorizingCreditNote, setIsAuthorizingCreditNote] = useState(false);

  useEffect(() => {
    const caseIdValue = caseIdFromLocation || caseIdFromQuery;
    const caseIdToUse = caseIdValue ? parseInt(caseIdValue, 10) : 0;
    setCaseId(caseIdToUse);
  }, [setCaseId, caseIdFromQuery, caseIdFromLocation]);

  const initAmountsFromInvoice = (invoice: IElligibleInvoiceResponse) => {
    const initialAmounts: { [key: string]: number } = {};
    invoice.items?.forEach((item) => {
      if (item.billing_section_id) initialAmounts[item.billing_section_id] = 0;
    });
    setCreditNoteAmounts(initialAmounts);
  };

  const handleCreateCreditNoteClick = () => {
    setSelectedAdjustment(null); // ✅ limpia contexto de ajuste si venimos por “crear”
    setShowEligibleInvoices(true);
  };

  const handleInvoiceSelect = (invoice: IElligibleInvoiceResponse) => {
    setSelectedInvoice(invoice);
    initAmountsFromInvoice(invoice);
  };

  const handleInvoiceChange: React.ChangeEventHandler<HTMLSelectElement> = (e) => {
    const value = e.target.value;
    const invoice = eligibleInvoices.find(inv => inv.invoice_number === value);
    setSelectedAdjustment(null); // ✅ si cambia factura manualmente, ocultar “Autorizar”
    if (invoice) {
      handleInvoiceSelect(invoice);
    } else {
      setSelectedInvoice(null);
      setCreditNoteAmounts({});
    }
  };

  const handleAmountChange = (billingSeccionId: string, value: number, maxAmount: number) => {
    if (value >= 0 && value <= maxAmount) {
      setCreditNoteAmounts(prev => ({ ...prev, [billingSeccionId]: value }));
    }
  };

  const handleCreateCreditNote = async () => {
    if (!selectedInvoice || !selectedInvoice.invoice_number) return;

    const details = Object.entries(creditNoteAmounts)
      .filter(([_, amount]) => amount > 0)
      .map(([billingSeccionId, amount]) => ({
        idRubro: parseInt(billingSeccionId, 10),
        amount
      }));

    if (details.length === 0) {
      setSnackBarError("Debe ingresar al menos un monto mayor a 0");
      return;
    }

    const success = await createCreditNoteFromAdjustment({
      invoiceId: parseInt(selectedInvoice.invoice_number, 10),
      details
    });

    if (success) {
      alert("Credit note creada exitosamente");
      setSelectedInvoice(null);
      setCreditNoteAmounts({});
      setSelectedAdjustment(null);
      setShowEligibleInvoices(false);
    }
  };

  const handleBackToList = () => {
    setSelectedInvoice(null);
    setSelectedAdjustment(null);
    setShowEligibleInvoices(false);
    setCreditNoteAmounts({});
  };

  const handleAuthorizeCreditNote = async () => {
    if (!selectedAdjustment || !selectedAdjustment.id) {
      setSnackBarError("No hay adjustment seleccionado para autorizar");
      return;
    }

    setIsAuthorizingCreditNote(true);

    try {
      // Por defecto, autorizamos con status "approved"
      // En el futuro se puede agregar lógica para determinar el status según el contexto
      const success = await updateAdjustmentStatus({
        adjustmentId: selectedAdjustment.id,
        status: "approved",
        adjustment: selectedAdjustment,
      });

      if (success) {
        alert("Credit note autorizada exitosamente");
        // Limpiar la selección después de autorizar
        setSelectedAdjustment(null);
        setShowEligibleInvoices(false);
        setSelectedInvoice(null);
        setCreditNoteAmounts({});
      }
    } catch (error) {
      console.error("Error autorizando credit note:", error);
    } finally {
      setIsAuthorizingCreditNote(false);
    }
  };

  // ✅ NUEVO: Ver Detalles desde la tabla de ajustes
  const handleViewDetailsFromAdjustment = (adj: IAdjustmentResponseDTO) => {
    // 1) Validar si tenemos la factura asociada
    const invoice = eligibleInvoices.find(inv => {
      const invNum = inv.invoice_number ? parseInt(inv.invoice_number, 10) : NaN;
      return Number.isFinite(invNum) && invNum === adj.invoiceId;
    });

    if (!invoice) {
      setSnackBarError(
        "No se encontraron los datos de la factura asociada a este ajuste."
      );
      return;
    }

    // 2) Abrir componente “Crear Credit Note”, setear case y factura
    setShowEligibleInvoices(true);
    setCaseId(adj.caseId ?? 0);
    setSelectedAdjustment(adj);
    setSelectedInvoice(invoice);

    // 3) Inicializar montos con items de la factura
    const initial: { [key: string]: number } = {};
    invoice.items?.forEach((item) => {
      if (item.billing_section_id) initial[item.billing_section_id] = 0;
    });

    // 4) Sobrescribir con montos del ajuste (idRubro -> amount)
    adj.detail?.forEach(d => {
      initial[String(d.idRubro)] = d.amount ?? 0;
    });

    setCreditNoteAmounts(initial);
  };

  const showAuthorizeButton =
    !!selectedAdjustment &&
    !["approved", "completed"].includes(
      (selectedAdjustment.detail?.[0]?.status || "").toLowerCase()
    ); // ✅ regla solicitada

  return (
    <div className="p-4">
      <h2 className="text-xl font-bold mb-4">
        {showEligibleInvoices ? "Crear Credit Note" : "Lista de Ajustes"}
      </h2>

      {/* Botones */}
      <div className="mb-4 space-x-2">
        {!showEligibleInvoices && (
          <button
            onClick={handleCreateCreditNoteClick}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            Crear Credit Note
          </button>
        )}

        <button
          onClick={loadAdjustmentsFromEligibleInvoices}
          disabled={isLoadingAdjustments}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-400"
        >
          {isLoadingAdjustments ? "Cargando..." : "Recargar Ajustes"}
        </button>

        {showEligibleInvoices && (
          <button
            onClick={handleBackToList}
            className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            Volver a Ajustes
          </button>
        )}
      </div>

      {/* Estado de carga */}
      {isLoadingAdjustments && (
        <div className="text-center py-4">
          <p>Cargando...</p>
        </div>
      )}

      {/* ==== Componente “Crear Credit Note” (select + detalle) ==== */}
      {showEligibleInvoices && !isLoadingAdjustments && (
        <div className="space-y-4">
          {/* CaseId input + Verificar */}
          <div className="flex flex-col sm:flex-row sm:items-end gap-2">
            <label className="text-sm font-medium sm:w-32">Case Id</label>
            <input
              type="number"
              value={Number.isFinite(caseId) ? caseId : 0}
              onChange={(e) => {
                const v = parseInt(e.target.value, 10);
                setCaseId(Number.isFinite(v) ? v : 0);
              }}
              className="w-full sm:max-w-xs px-3 py-2 border rounded"
              placeholder="Ingrese Case Id"
            />
            <button
              type="button"
              className="px-4 py-2 bg-indigo-500 text-white rounded hover:bg-indigo-600"
            >
              Verificar
            </button>
          </div>

          {/* Select de facturas elegibles */}
          <div className="flex flex-col sm:flex-row sm:items-end gap-2">
            <label className="text-sm font-medium sm:w-32">Factura</label>
            <select
              className="w-full sm:max-w-xs px-3 py-2 border rounded"
              value={selectedInvoice?.invoice_number ?? ""}
              onChange={handleInvoiceChange}
            >
              <option value="" disabled>
                {eligibleInvoices.length === 0
                  ? "No hay facturas elegibles"
                  : "Seleccione una factura..."}
              </option>
              {eligibleInvoices.map((inv, idx) => (
                <option key={`${inv.invoice_number}-${idx}`} value={inv.invoice_number ?? ""}>
                  {inv.invoice_number} ({inv.items?.length ?? 0} ítems)
                </option>
              ))}
            </select>
          </div>

          {/* Detalle factura */}
          {selectedInvoice && (
            <div className="space-y-4">
              <div className="p-4 bg-gray-100 rounded">
                <h3 className="font-bold">Factura: {selectedInvoice.invoice_number}</h3>
              </div>

              <table className="min-w-full border border-gray-300 rounded-lg overflow-hidden mb-4">
                <thead className="bg-gray-100">
                  <tr>
                    <th className="px-4 py-2 border-b text-left">Billing Section ID</th>
                    <th className="px-4 py-2 border-b text-left">Descripción</th>
                    <th className="px-4 py-2 border-b text-left">Monto Disponible</th>
                    <th className="px-4 py-2 border-b text-left">Monto Credit Note</th>
                  </tr>
                </thead>
                <tbody>
                  {selectedInvoice.items?.map((item, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="px-4 py-2 border-b">{item.billing_section_id}</td>
                      <td className="px-4 py-2 border-b">{item.description}</td>
                      <td className="px-4 py-2 border-b">
                        ${(item.net_amount_left || 0).toFixed(2)}
                      </td>
                      <td className="px-4 py-2 border-b">
                        <input
                          type="number"
                          min="0"
                          max={item.net_amount_left || 0}
                          step="0.01"
                          value={creditNoteAmounts[item.billing_section_id || ""] || 0}
                          onChange={(e) => {
                            const value = parseFloat(e.target.value) || 0;
                            handleAmountChange(
                              item.billing_section_id || "",
                              value,
                              item.net_amount_left || 0
                            );
                          }}
                          className="w-full px-2 py-1 border rounded"
                        />
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>

              <div className="flex justify-end gap-2">
                {/* ✅ Botón condicional */}
                {showAuthorizeButton && (
                  <button
                    type="button"
                    onClick={handleAuthorizeCreditNote}
                    disabled={isAuthorizingCreditNote}
                    className={`px-4 py-2 text-white rounded ${
                      isAuthorizingCreditNote
                        ? 'bg-gray-400 cursor-not-allowed'
                        : 'bg-yellow-500 hover:bg-yellow-600'
                    }`}
                  >
                    {isAuthorizingCreditNote ? "Autorizando..." : "Autorizar Credit Note"}
                  </button>
                )}

                <button
                  onClick={handleCreateCreditNote}
                  className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
                >
                  Crear Credit Note
                </button>
              </div>
            </div>
          )}
        </div>
      )}

      {/* ==== Tabla de ajustes (se agrega columna “Detalles”) ==== */}
      {!showEligibleInvoices && !isLoadingAdjustments && (
        <table className="min-w-full border border-gray-300 rounded-lg overflow-hidden">
          <thead className="bg-gray-100">
            <tr>
              <th className="px-4 py-2 border-b text-left">Ajuste</th>
              <th className="px-4 py-2 border-b text-left">Factura</th>
              <th className="px-4 py-2 border-b text-left">Caso</th>
              <th className="px-4 py-2 border-b text-left">Monto Total</th>
              <th className="px-4 py-2 border-b text-left">Tipo</th>
              <th className="px-4 py-2 border-b text-left">Status</th>
              <th className="px-4 py-2 border-b text-left">Fecha de Creación</th>
              <th className="px-4 py-2 border-b text-left">Creado Por</th>
              <th className="px-4 py-2 border-b text-left">Detalles</th> {/* ✅ NUEVO */}
            </tr>
          </thead>
          <tbody>
            {adjustmentsList.length === 0 ? (
              <tr>
                {/* ✅ ajustar colspan a 9 por nueva columna */}
                <td colSpan={9} className="px-4 py-2 border-b text-center text-gray-500">
                  No se encontraron ajustes
                </td>
              </tr>
            ) : (
              adjustmentsList.map((adjustment) => (
                <tr key={adjustment.id} className="hover:bg-gray-50">
                  <td className="px-4 py-2 border-b">{adjustment.id}</td>
                  <td className="px-4 py-2 border-b">{adjustment.invoiceId}</td>
                  <td className="px-4 py-2 border-b">{adjustment.caseId}</td>
                  <td className="px-4 py-2 border-b">
                    ${adjustment.totalAmount?.toFixed(2) || "0.00"}
                  </td>
                  <td className="px-4 py-2 border-b">{adjustment.type || ""}</td>
                  <td className="px-4 py-2 border-b">{adjustment.detail?.[0]?.status || ""}</td>
                  <td className="px-4 py-2 border-b">{adjustment.createdDate || ""}</td>
                  <td className="px-4 py-2 border-b">{adjustment.createdBy || "N/A"}</td>
                  <td className="px-4 py-2 border-b">
                    <button
                      type="button"
                      onClick={() => handleViewDetailsFromAdjustment(adjustment)}
                      className="px-2 py-1 bg-purple-500 text-white rounded hover:bg-purple-600 text-sm"
                    >
                      Detalles
                    </button>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      )}
    </div>
  );
};

export default ViewAdjusments;

